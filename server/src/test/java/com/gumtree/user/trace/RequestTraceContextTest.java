package com.gumtree.user.trace;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.slf4j.MDC;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 请求追踪上下文测试
 */
public class RequestTraceContextTest {

    @BeforeEach
    public void setUp() {
        // 清理MDC，确保测试环境干净
        MDC.clear();
        RequestTraceContext.clear();
    }

    @AfterEach
    public void tearDown() {
        // 测试后清理
        RequestTraceContext.clear();
    }

    @Test
    public void testSetAndGetTraceId() {
        String traceId = "test123456";
        
        // 设置追踪ID
        RequestTraceContext.setTraceId(traceId);
        
        // 验证可以获取到追踪ID
        assertEquals(traceId, RequestTraceContext.getTraceId());
        assertEquals(traceId, MDC.get(RequestTraceContext.TRACE_ID_KEY));
        assertTrue(RequestTraceContext.hasTraceId());
    }

    @Test
    public void testSetRequestStartTime() {
        long startTime = System.currentTimeMillis();
        
        // 设置请求开始时间
        RequestTraceContext.setRequestStartTime(startTime);
        
        // 验证可以获取到开始时间
        assertEquals(startTime, RequestTraceContext.getRequestStartTime());
        assertEquals(String.valueOf(startTime), MDC.get(RequestTraceContext.REQUEST_START_TIME_KEY));
    }

    @Test
    public void testSetRequestUri() {
        String uri = "/users/123";
        
        // 设置请求URI
        RequestTraceContext.setRequestUri(uri);
        
        // 验证MDC中有URI信息
        assertEquals(uri, MDC.get(RequestTraceContext.REQUEST_URI_KEY));
    }

    @Test
    public void testSetHttpMethod() {
        String method = "POST";
        
        // 设置HTTP方法
        RequestTraceContext.setHttpMethod(method);
        
        // 验证MDC中有方法信息
        assertEquals(method, MDC.get(RequestTraceContext.HTTP_METHOD_KEY));
    }

    @Test
    public void testClear() {
        // 设置一些数据
        RequestTraceContext.setTraceId("test123");
        RequestTraceContext.setRequestStartTime(System.currentTimeMillis());
        RequestTraceContext.setRequestUri("/test");
        RequestTraceContext.setHttpMethod("GET");
        
        // 验证数据已设置
        assertNotNull(RequestTraceContext.getTraceId());
        assertTrue(RequestTraceContext.getRequestStartTime() > 0);
        
        // 清理
        RequestTraceContext.clear();
        
        // 验证数据已清理
        assertNull(RequestTraceContext.getTraceId());
        assertEquals(0L, RequestTraceContext.getRequestStartTime());
        assertFalse(RequestTraceContext.hasTraceId());
        
        // 验证MDC也已清理
        assertNull(MDC.get(RequestTraceContext.TRACE_ID_KEY));
        assertNull(MDC.get(RequestTraceContext.REQUEST_START_TIME_KEY));
        assertNull(MDC.get(RequestTraceContext.REQUEST_URI_KEY));
        assertNull(MDC.get(RequestTraceContext.HTTP_METHOD_KEY));
    }

    @Test
    public void testHasTraceId() {
        // 初始状态应该没有追踪ID
        assertFalse(RequestTraceContext.hasTraceId());
        
        // 设置追踪ID后应该有
        RequestTraceContext.setTraceId("test123");
        assertTrue(RequestTraceContext.hasTraceId());
        
        // 清理后应该没有
        RequestTraceContext.clear();
        assertFalse(RequestTraceContext.hasTraceId());
    }

    @Test
    public void testGetRequestStartTimeWithInvalidValue() {
        // 设置无效的开始时间
        MDC.put(RequestTraceContext.REQUEST_START_TIME_KEY, "invalid");
        
        // 应该返回0
        assertEquals(0L, RequestTraceContext.getRequestStartTime());
    }

    @Test
    public void testSetNullValues() {
        // 设置null值应该不会抛异常
        assertDoesNotThrow(() -> {
            RequestTraceContext.setRequestUri(null);
            RequestTraceContext.setHttpMethod(null);
        });
        
        // MDC中不应该有这些键
        assertNull(MDC.get(RequestTraceContext.REQUEST_URI_KEY));
        assertNull(MDC.get(RequestTraceContext.HTTP_METHOD_KEY));
    }
}
