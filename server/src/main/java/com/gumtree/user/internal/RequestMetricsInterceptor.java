package com.gumtree.user.internal;

import com.gumtree.user.trace.RequestTraceContext;
import io.micrometer.core.instrument.Clock;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.HandlerMapping;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

@Component
public class RequestMetricsInterceptor implements HandlerInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(RequestMetricsInterceptor.class);

    static final String REQUEST_TIMER_ATTR = "request_timer_start";
    static final String HTTP_SERVER_REQUESTS = "http_server_requests";

    private final MeterRegistry meterRegistry;

    @Autowired
    public RequestMetricsInterceptor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        request.setAttribute(REQUEST_TIMER_ATTR, Clock.SYSTEM.monotonicTime());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception exception) {
        Long startTime = (Long)request.getAttribute(REQUEST_TIMER_ATTR);
        if (null != startTime) {
            long durationNs = Clock.SYSTEM.monotonicTime() - startTime;
            Timer timer = buildTimer(request, getResponseStatus(response));
            timer.record(durationNs, TimeUnit.NANOSECONDS);

            // 记录请求指标日志
            logRequestMetrics(request, response, durationNs, exception);
        }
    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response,
                           Object handler, ModelAndView modelAndView) throws Exception {
    }

    private Timer buildTimer(HttpServletRequest webRequest, Integer status) {
        String traceId = RequestTraceContext.getTraceId();
        Timer.Builder timerBuilder = Timer.builder(HTTP_SERVER_REQUESTS)
                .tag("uri", getRequestUri(webRequest))
                .tag("method", getHttpMethod(webRequest))
                .tag("status", status.toString())
                .publishPercentiles(0.5, 0.95, 0.99);

        // 添加追踪ID标签（如果存在）
        if (traceId != null) {
            timerBuilder.tag("traceId", traceId);
        }

        return timerBuilder.register(meterRegistry);
    }

    private String getHttpMethod(HttpServletRequest webRequest) {
        return webRequest.getMethod().toLowerCase();
    }

    private String getRequestUri(HttpServletRequest request) {
        return Optional
                .ofNullable(request.getAttribute(HandlerMapping.BEST_MATCHING_PATTERN_ATTRIBUTE))
                .map(Object::toString)
                .orElseGet(request::getRequestURI);
    }

    private Integer getResponseStatus(HttpServletResponse response) {
        return response.getStatus();
    }

    /**
     * 记录请求指标日志
     */
    private void logRequestMetrics(HttpServletRequest request, HttpServletResponse response,
                                 long durationNs, Exception exception) {
        if (logger.isDebugEnabled()) {
            long durationMs = TimeUnit.NANOSECONDS.toMillis(durationNs);
            String traceId = RequestTraceContext.getTraceId();

            StringBuilder logMessage = new StringBuilder();
            logMessage.append("[METRICS] Request processed - ");
            logMessage.append("TraceId: ").append(traceId != null ? traceId : "NO_TRACE").append(", ");
            logMessage.append("Method: ").append(request.getMethod()).append(", ");
            logMessage.append("URI: ").append(getRequestUri(request)).append(", ");
            logMessage.append("Status: ").append(response.getStatus()).append(", ");
            logMessage.append("Duration: ").append(durationMs).append("ms");

            if (exception != null) {
                logMessage.append(", Exception: ").append(exception.getClass().getSimpleName());
                logger.debug(logMessage.toString(), exception);
            } else {
                logger.debug(logMessage.toString());
            }
        }
    }
}
