package com.gumtree.user.internal;

import org.slf4j.MDC;
import java.util.UUID;

public class RequestTraceUtils {
    public static final String TRACE_ID = "traceId";
    
    public static void setTraceId() {
        MDC.put(TRACE_ID, generateTraceId());
    }
    
    public static void setTraceId(String traceId) {
        MDC.put(TRACE_ID, traceId);
    }
    
    public static String getTraceId() {
        return MDC.get(TRACE_ID);
    }
    
    public static void clearTraceId() {
        MDC.remove(TRACE_ID);
    }
    
    private static String generateTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}