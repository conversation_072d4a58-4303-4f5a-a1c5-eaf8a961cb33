package com.gumtree.user.trace;

import org.slf4j.MDC;

/**
 * 请求追踪上下文管理器
 * 使用ThreadLocal和SLF4J MDC来管理当前线程的追踪ID
 */
public class RequestTraceContext {
    
    /**
     * 追踪ID在MDC中的键名
     */
    public static final String TRACE_ID_KEY = "traceId";
    
    /**
     * 请求开始时间在MDC中的键名
     */
    public static final String REQUEST_START_TIME_KEY = "requestStartTime";
    
    /**
     * 请求URI在MDC中的键名
     */
    public static final String REQUEST_URI_KEY = "requestUri";
    
    /**
     * HTTP方法在MDC中的键名
     */
    public static final String HTTP_METHOD_KEY = "httpMethod";
    
    private static final ThreadLocal<String> traceIdHolder = new ThreadLocal<>();
    
    /**
     * 设置当前线程的追踪ID
     * 
     * @param traceId 追踪ID
     */
    public static void setTraceId(String traceId) {
        traceIdHolder.set(traceId);
        MDC.put(TRACE_ID_KEY, traceId);
    }
    
    /**
     * 获取当前线程的追踪ID
     * 
     * @return 追踪ID，如果没有设置则返回null
     */
    public static String getTraceId() {
        return traceIdHolder.get();
    }
    
    /**
     * 设置请求开始时间
     * 
     * @param startTime 开始时间（毫秒时间戳）
     */
    public static void setRequestStartTime(long startTime) {
        MDC.put(REQUEST_START_TIME_KEY, String.valueOf(startTime));
    }
    
    /**
     * 设置请求URI
     * 
     * @param uri 请求URI
     */
    public static void setRequestUri(String uri) {
        if (uri != null) {
            MDC.put(REQUEST_URI_KEY, uri);
        }
    }
    
    /**
     * 设置HTTP方法
     * 
     * @param method HTTP方法
     */
    public static void setHttpMethod(String method) {
        if (method != null) {
            MDC.put(HTTP_METHOD_KEY, method);
        }
    }
    
    /**
     * 获取请求开始时间
     * 
     * @return 开始时间，如果没有设置则返回0
     */
    public static long getRequestStartTime() {
        String startTime = MDC.get(REQUEST_START_TIME_KEY);
        if (startTime != null) {
            try {
                return Long.parseLong(startTime);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }
    
    /**
     * 清理当前线程的所有追踪信息
     */
    public static void clear() {
        traceIdHolder.remove();
        MDC.remove(TRACE_ID_KEY);
        MDC.remove(REQUEST_START_TIME_KEY);
        MDC.remove(REQUEST_URI_KEY);
        MDC.remove(HTTP_METHOD_KEY);
    }
    
    /**
     * 检查当前线程是否有追踪ID
     * 
     * @return 如果有追踪ID返回true，否则返回false
     */
    public static boolean hasTraceId() {
        return getTraceId() != null;
    }
}
