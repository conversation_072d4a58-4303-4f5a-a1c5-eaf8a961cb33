package com.gumtree.user.trace;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 请求追踪过滤器
 * 为每个HTTP请求生成唯一的追踪ID，并在请求处理完成后清理
 */
public class RequestTraceFilter implements Filter {
    
    private static final Logger logger = LoggerFactory.getLogger(RequestTraceFilter.class);
    
    /**
     * HTTP头中追踪ID的键名
     * 如果请求头中包含此字段，将使用该值作为追踪ID
     */
    public static final String TRACE_ID_HEADER = "X-Trace-Id";
    
    /**
     * 响应头中追踪ID的键名
     * 将追踪ID添加到响应头中，便于客户端追踪
     */
    public static final String RESPONSE_TRACE_ID_HEADER = "X-Trace-Id";
    
    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        logger.info("RequestTraceFilter initialized");
    }
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) 
            throws IOException, ServletException {
        
        if (!(request instanceof HttpServletRequest) || !(response instanceof HttpServletResponse)) {
            chain.doFilter(request, response);
            return;
        }
        
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        
        // 生成或获取追踪ID
        String traceId = getOrGenerateTraceId(httpRequest);
        
        try {
            // 设置追踪上下文
            setupTraceContext(httpRequest, traceId);
            
            // 将追踪ID添加到响应头
            httpResponse.setHeader(RESPONSE_TRACE_ID_HEADER, traceId);
            
            // 记录请求开始日志
            logRequestStart(httpRequest, traceId);
            
            // 继续处理请求
            chain.doFilter(request, response);
            
            // 记录请求完成日志
            logRequestEnd(httpRequest, httpResponse, traceId);
            
        } finally {
            // 清理追踪上下文
            RequestTraceContext.clear();
        }
    }
    
    @Override
    public void destroy() {
        logger.info("RequestTraceFilter destroyed");
    }
    
    /**
     * 获取或生成追踪ID
     * 优先使用请求头中的追踪ID，如果没有则生成新的
     */
    private String getOrGenerateTraceId(HttpServletRequest request) {
        String traceId = request.getHeader(TRACE_ID_HEADER);
        
        if (traceId != null && TraceIdGenerator.isValidTraceId(traceId)) {
            return traceId.trim();
        }
        
        return TraceIdGenerator.generate();
    }
    
    /**
     * 设置追踪上下文
     */
    private void setupTraceContext(HttpServletRequest request, String traceId) {
        RequestTraceContext.setTraceId(traceId);
        RequestTraceContext.setRequestStartTime(System.currentTimeMillis());
        RequestTraceContext.setRequestUri(request.getRequestURI());
        RequestTraceContext.setHttpMethod(request.getMethod());
    }
    
    /**
     * 记录请求开始日志
     */
    private void logRequestStart(HttpServletRequest request, String traceId) {
        String clientIp = getClientIpAddress(request);
        String userAgent = request.getHeader("User-Agent");
        String queryString = request.getQueryString();
        
        StringBuilder logMessage = new StringBuilder();
        logMessage.append("Request started - ");
        logMessage.append("Method: ").append(request.getMethod()).append(", ");
        logMessage.append("URI: ").append(request.getRequestURI());
        
        if (queryString != null && !queryString.isEmpty()) {
            logMessage.append("?").append(queryString);
        }
        
        logMessage.append(", Client IP: ").append(clientIp);
        
        if (userAgent != null && !userAgent.isEmpty()) {
            logMessage.append(", User-Agent: ").append(userAgent);
        }
        
        logger.info(logMessage.toString());
    }
    
    /**
     * 记录请求完成日志
     */
    private void logRequestEnd(HttpServletRequest request, HttpServletResponse response, String traceId) {
        long startTime = RequestTraceContext.getRequestStartTime();
        long duration = System.currentTimeMillis() - startTime;
        
        logger.info("Request completed - Status: {}, Duration: {}ms, Method: {}, URI: {}", 
                response.getStatus(), duration, request.getMethod(), request.getRequestURI());
    }
    
    /**
     * 获取客户端真实IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }
}
