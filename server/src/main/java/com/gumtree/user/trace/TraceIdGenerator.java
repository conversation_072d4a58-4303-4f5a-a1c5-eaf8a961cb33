package com.gumtree.user.trace;

import java.util.UUID;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 追踪ID生成器
 * 提供多种生成策略来创建唯一的请求追踪ID
 */
public class TraceIdGenerator {
    
    /**
     * 生成基于UUID的追踪ID
     * 格式：去掉连字符的UUID，例如：a1b2c3d4e5f6789012345678901234ab
     * 
     * @return 32位的十六进制字符串
     */
    public static String generateUuidTraceId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
    
    /**
     * 生成短格式的追踪ID
     * 格式：时间戳(13位) + 随机数(3位)，例如：1704067200123456
     * 
     * @return 16位的数字字符串
     */
    public static String generateShortTraceId() {
        long timestamp = System.currentTimeMillis();
        int random = ThreadLocalRandom.current().nextInt(100, 1000);
        return timestamp + String.valueOf(random);
    }
    
    /**
     * 生成紧凑格式的追踪ID
     * 格式：时间戳后8位 + 6位随机十六进制，例如：67200123a1b2c3
     * 
     * @return 14位的十六进制字符串
     */
    public static String generateCompactTraceId() {
        long timestamp = System.currentTimeMillis();
        String timestampSuffix = String.valueOf(timestamp).substring(5); // 取后8位
        String randomHex = String.format("%06x", ThreadLocalRandom.current().nextInt(0, 0xFFFFFF));
        return timestampSuffix + randomHex;
    }
    
    /**
     * 生成默认格式的追踪ID
     * 当前使用紧凑格式，平衡了唯一性和可读性
     * 
     * @return 追踪ID字符串
     */
    public static String generate() {
        return generateCompactTraceId();
    }
    
    /**
     * 验证追踪ID格式是否有效
     * 
     * @param traceId 要验证的追踪ID
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidTraceId(String traceId) {
        if (traceId == null || traceId.trim().isEmpty()) {
            return false;
        }
        
        // 检查长度和字符
        String trimmed = traceId.trim();
        if (trimmed.length() < 8 || trimmed.length() > 32) {
            return false;
        }
        
        // 检查是否只包含字母数字字符
        return trimmed.matches("^[a-zA-Z0-9]+$");
    }
}
