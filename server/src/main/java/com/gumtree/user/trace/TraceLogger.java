package com.gumtree.user.trace;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 追踪日志工具类
 * 提供便捷的方法来记录带有追踪ID的日志
 */
public class TraceLogger {
    
    /**
     * 获取带有追踪信息的Logger
     * 
     * @param clazz 调用类
     * @return Logger实例
     */
    public static Logger getLogger(Class<?> clazz) {
        return LoggerFactory.getLogger(clazz);
    }
    
    /**
     * 记录INFO级别的业务日志
     * 
     * @param logger Logger实例
     * @param message 日志消息
     * @param args 消息参数
     */
    public static void info(Logger logger, String message, Object... args) {
        if (logger.isInfoEnabled()) {
            logger.info(formatMessage(message), args);
        }
    }
    
    /**
     * 记录WARN级别的业务日志
     * 
     * @param logger Logger实例
     * @param message 日志消息
     * @param args 消息参数
     */
    public static void warn(Logger logger, String message, Object... args) {
        if (logger.isWarnEnabled()) {
            logger.warn(formatMessage(message), args);
        }
    }
    
    /**
     * 记录ERROR级别的业务日志
     * 
     * @param logger Logger实例
     * @param message 日志消息
     * @param throwable 异常信息
     * @param args 消息参数
     */
    public static void error(Logger logger, String message, Throwable throwable, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(formatMessage(message), args, throwable);
        }
    }
    
    /**
     * 记录ERROR级别的业务日志（无异常）
     * 
     * @param logger Logger实例
     * @param message 日志消息
     * @param args 消息参数
     */
    public static void error(Logger logger, String message, Object... args) {
        if (logger.isErrorEnabled()) {
            logger.error(formatMessage(message), args);
        }
    }
    
    /**
     * 记录DEBUG级别的业务日志
     * 
     * @param logger Logger实例
     * @param message 日志消息
     * @param args 消息参数
     */
    public static void debug(Logger logger, String message, Object... args) {
        if (logger.isDebugEnabled()) {
            logger.debug(formatMessage(message), args);
        }
    }
    
    /**
     * 记录业务操作开始日志
     * 
     * @param logger Logger实例
     * @param operation 操作名称
     * @param params 操作参数
     */
    public static void logOperationStart(Logger logger, String operation, Object... params) {
        if (logger.isInfoEnabled()) {
            StringBuilder message = new StringBuilder();
            message.append("Operation started - ").append(operation);
            if (params != null && params.length > 0) {
                message.append(" with params: ");
                for (int i = 0; i < params.length; i++) {
                    if (i > 0) message.append(", ");
                    message.append("{}");
                }
            }
            logger.info(formatMessage(message.toString()), params);
        }
    }
    
    /**
     * 记录业务操作完成日志
     * 
     * @param logger Logger实例
     * @param operation 操作名称
     * @param startTime 开始时间（毫秒）
     * @param result 操作结果
     */
    public static void logOperationEnd(Logger logger, String operation, long startTime, Object result) {
        if (logger.isInfoEnabled()) {
            long duration = System.currentTimeMillis() - startTime;
            logger.info(formatMessage("Operation completed - {} in {}ms, result: {}"), 
                    operation, duration, result);
        }
    }
    
    /**
     * 记录业务操作失败日志
     * 
     * @param logger Logger实例
     * @param operation 操作名称
     * @param startTime 开始时间（毫秒）
     * @param throwable 异常信息
     */
    public static void logOperationError(Logger logger, String operation, long startTime, Throwable throwable) {
        if (logger.isErrorEnabled()) {
            long duration = System.currentTimeMillis() - startTime;
            logger.error(formatMessage("Operation failed - {} after {}ms"), 
                    operation, duration, throwable);
        }
    }
    
    /**
     * 格式化日志消息，添加业务标识
     * 
     * @param message 原始消息
     * @return 格式化后的消息
     */
    private static String formatMessage(String message) {
        return "[BUSINESS] " + message;
    }
    
    /**
     * 获取当前请求的追踪ID
     * 
     * @return 追踪ID，如果没有则返回"NO_TRACE"
     */
    public static String getCurrentTraceId() {
        String traceId = RequestTraceContext.getTraceId();
        return traceId != null ? traceId : "NO_TRACE";
    }
}
