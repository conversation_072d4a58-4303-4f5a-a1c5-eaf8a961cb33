package com.gumtree.user.mvc.controller;

import com.gumtree.bapi.service.api.errors.BapiException;
import com.gumtree.bapi.service.model.User;
import com.gumtree.user.converter.UserResponseConverter;
import com.gumtree.user.exception.BadRequestException;
import com.gumtree.user.mvc.validation.UserRegistrationRequestValidator;
import com.gumtree.user.registration.UserRegistrationStrategy;
import com.gumtree.user.service.IdValidator;
import com.gumtree.user.service.UserService;
import com.gumtree.user.trace.TraceLogger;
import com.gumtree.userapi.model.RegisteredUser;
import com.gumtree.userapi.model.UserLastLoggedInDateTime;
import com.gumtree.userapi.model.UserRegistrationRequest;
import com.gumtree.userapi.model.UserResponse;
import com.gumtree.userapi.model.error.UserApiErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.validation.Valid;
import java.util.Set;
import java.util.function.Consumer;

import static com.gumtree.userapi.model.RequestMappings.CREATE_USER;
import static com.gumtree.userapi.model.RequestMappings.GET_USER_ID;
import static com.gumtree.userapi.model.RequestMappings.REGISTER_USER;
import static com.gumtree.userapi.model.RequestMappings.USERS;
import static org.springframework.http.MediaType.APPLICATION_JSON_VALUE;
import static org.springframework.web.bind.annotation.RequestMethod.GET;
import static org.springframework.web.bind.annotation.RequestMethod.POST;
import static org.springframework.web.bind.annotation.RequestMethod.PUT;

@Controller
@Validated
public class UserController {

    private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class);

    private Set<UserRegistrationStrategy> strategies;
    private UserService userService;
    private Consumer<String> emailValidator;
    private IdValidator idValidator;
    private UserRegistrationRequestValidator userRegistrationRequestValidator;

    @Autowired
    public UserController(UserService userService, Set<UserRegistrationStrategy> strategies,
                          Consumer<String> emailValidator, IdValidator idValidator,
                          UserRegistrationRequestValidator userRegistrationRequestValidator) {
        this.userService = userService;
        this.emailValidator = emailValidator;
        this.idValidator = idValidator;
        this.strategies = strategies;
        this.userRegistrationRequestValidator = userRegistrationRequestValidator;
    }

    /**
     * @deprecated use the other method registerUser with @ResponseStatus(HttpStatus.OK), we need to keep this one
     * till we migrate to the new one
     */
    @RequestMapping(value = CREATE_USER, method = POST, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    @ResponseStatus(HttpStatus.NO_CONTENT)
    public HttpEntity register(@RequestBody @Valid UserRegistrationRequest request) throws BapiException {
        long startTime = System.currentTimeMillis();

        TraceLogger.logOperationStart(LOGGER, "user_registration_legacy",
                request.getFirstName() + " " + request.getLastName(),
                request.getUsername(),
                request.getThreatMetrixSessionId());

        try {
            userRegistrationRequestValidator.validateRegistrationRequest(request);
            UserRegistrationStrategy strategy = findStrategy(request);
            strategy.register(request);

            TraceLogger.logOperationEnd(LOGGER, "user_registration_legacy", startTime, "SUCCESS");
            return HttpEntity.EMPTY;
        } catch (Exception e) {
            TraceLogger.logOperationError(LOGGER, "user_registration_legacy", startTime, e);
            throw e;
        }
    }

    @RequestMapping(value = REGISTER_USER, method = POST, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public RegisteredUser registerUser(@RequestBody @Valid UserRegistrationRequest request) throws BapiException {
        long startTime = System.currentTimeMillis();

        TraceLogger.logOperationStart(LOGGER, "user_registration",
                request.getFirstName() + " " + request.getLastName(),
                request.getUsername(),
                request.getThreatMetrixSessionId());

        try {
            userRegistrationRequestValidator.validateRegistrationRequest(request);
            UserRegistrationStrategy strategy = findStrategy(request);
            RegisteredUser result = strategy.register(request);

            TraceLogger.logOperationEnd(LOGGER, "user_registration", startTime,
                    "userId=" + (result != null ? result.getUserId() : "null"));
            return result;
        } catch (Exception e) {
            TraceLogger.logOperationError(LOGGER, "user_registration", startTime, e);
            throw e;
        }
    }


    @RequestMapping(value = GET_USER_ID, method = GET, produces = APPLICATION_JSON_VALUE, params = {"email!="})
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<Long> getUserIdByEmailAddress(@RequestParam("email") String email) throws BapiException {
        emailValidator.accept(email);

        return userService
                .getUserIdByEmail(email)
                .map(user ->
                new ResponseEntity<>(user, HttpStatus.OK)).orElseGet(() -> new ResponseEntity<>(HttpStatus.NO_CONTENT));
    }

    @RequestMapping(value = USERS + "/{email}/user", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<UserResponse> getUserByEmailAddress(@PathVariable("email") String email) throws BapiException {
        long startTime = System.currentTimeMillis();

        TraceLogger.logOperationStart(LOGGER, "get_user_by_email", email);

        try {
            emailValidator.accept(email);

            ResponseEntity<UserResponse> result = userService
                    .getUserByEmailAddress(email)
                    .map(UserController::convertToResponseEntity)
                    .orElseGet(() -> new ResponseEntity<>(HttpStatus.NO_CONTENT));

            TraceLogger.logOperationEnd(LOGGER, "get_user_by_email", startTime,
                    "status=" + result.getStatusCode());
            return result;
        } catch (Exception e) {
            TraceLogger.logOperationError(LOGGER, "get_user_by_email", startTime, e);
            throw e;
        }
    }

    @RequestMapping(value = USERS + "/{userId}", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<UserResponse> getUserById(@PathVariable("userId") Long userId) throws BapiException {
        idValidator.validate(userId);

        return userService
                .getUserById(userId)
                .map(UserController::convertToResponseEntity)
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NO_CONTENT));
    }

    @RequestMapping(value = USERS + "/{userId}/marketing-preference/{optInMarketing}", method = PUT, produces =
            APPLICATION_JSON_VALUE)
    @ResponseBody
    @ResponseStatus(HttpStatus.OK)
    public ResponseEntity<UserResponse> setMarketingPreference(@PathVariable("userId") Long userId,
                                                               @PathVariable("optInMarketing") Boolean marketingFlag) throws BapiException {
        idValidator.validate(userId);

        return userService
                .setOptInMarketing(userId, marketingFlag)
                .map(UserController::convertToResponseEntity)
                .orElseGet(() -> new ResponseEntity<>(HttpStatus.NO_CONTENT));
    }

    @RequestMapping(value = USERS + "/last-logged-in/{userId}", method = GET, produces = APPLICATION_JSON_VALUE)
    @ResponseBody
    public ResponseEntity<UserLastLoggedInDateTime> getUserLastLoggedInDateTime(@PathVariable("userId") long userId) throws BapiException {
        idValidator.validate(userId);

        return userService.getLastLoggedInDateTimeByUserId(userId)
                .map(lu -> new ResponseEntity<>(new UserLastLoggedInDateTime(lu), HttpStatus.OK))
                .orElse(new ResponseEntity<>(HttpStatus.NO_CONTENT));
    }

    private UserRegistrationStrategy findStrategy(UserRegistrationRequest request) {
        for (UserRegistrationStrategy strategy : strategies) {
            if (strategy.support(request.getAuthenticationProvider())) {
                return strategy;
            }
        }

        throw new BadRequestException(UserApiErrorCode.UNSUPPORTED_AUTHENTICATION_PROVIDER);
    }

    private static ResponseEntity<UserResponse> convertToResponseEntity(User userResponse) {
        if (UserResponseConverter.isUserValid(userResponse)) {
            return new ResponseEntity<>(UserResponseConverter.toUserResponse(userResponse), HttpStatus.OK);
        } else {
            return new ResponseEntity<>(HttpStatus.NON_AUTHORITATIVE_INFORMATION);
        }
    }

}
