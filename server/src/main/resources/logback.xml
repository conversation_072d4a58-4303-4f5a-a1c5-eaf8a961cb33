<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-NO_TRACE}] [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- 应用日志文件输出 -->
    <appender name="appFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/gumtree/user-service/application.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-NO_TRACE}] [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/log/gumtree/user-service/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 请求追踪日志文件输出 -->
    <appender name="traceFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/gumtree/user-service/request-trace.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-NO_TRACE}] %X{httpMethod:-} %X{requestUri:-} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/log/gumtree/user-service/request-trace.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>14</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <!-- 错误日志文件输出 -->
    <appender name="errorFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/gumtree/user-service/error.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-NO_TRACE}] [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/log/gumtree/user-service/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>WARN</level>
        </filter>
    </appender>

    <!-- 业务日志文件输出 -->
    <appender name="businessFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>/var/log/gumtree/user-service/business.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%X{traceId:-NO_TRACE}] [%thread] %-5level %logger{50} - %msg%n</pattern>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>/var/log/gumtree/user-service/business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <filter class="ch.qos.logback.core.filter.EvaluatorFilter">
            <evaluator>
                <expression>return message.contains("[BUSINESS]");</expression>
            </evaluator>
            <OnMismatch>DENY</OnMismatch>
            <OnMatch>ACCEPT</OnMatch>
        </filter>
    </appender>

    <!-- 请求追踪过滤器的日志 -->
    <logger name="com.gumtree.user.trace.RequestTraceFilter" level="INFO" additivity="false">
        <appender-ref ref="traceFile"/>
        <appender-ref ref="console"/>
    </logger>

    <!-- 业务控制器日志 -->
    <logger name="com.gumtree.user.mvc.controller" level="INFO" additivity="true">
        <appender-ref ref="businessFile"/>
    </logger>

    <!-- 业务服务日志 -->
    <logger name="com.gumtree.user.service" level="INFO" additivity="true">
        <appender-ref ref="businessFile"/>
    </logger>

    <!-- 认证相关日志 -->
    <logger name="com.gumtree.user.auth" level="INFO" additivity="true">
        <appender-ref ref="businessFile"/>
    </logger>

    <!-- 第三方库日志级别控制 -->
    <logger name="org.springframework" level="WARN"/>
    <logger name="org.springframework.jdbc.core" level="INFO"/>
    <logger name="org.hibernate" level="WARN"/>
    <logger name="com.netflix.hystrix" level="WARN"/>
    <logger name="feign" level="INFO"/>
    <logger name="com.gumtree.bapi" level="INFO"/>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="console"/>
        <appender-ref ref="appFile"/>
        <appender-ref ref="errorFile"/>
    </root>

</configuration>
