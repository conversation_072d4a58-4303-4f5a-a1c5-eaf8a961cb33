<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="http://java.sun.com/xml/ns/javaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
         version="3.0" metadata-complete="true">

    <display-name>User Service</display-name>
    <context-param>
        <param-name>contextClass</param-name>
        <param-value>
            org.springframework.web.context.support.AnnotationConfigWebApplicationContext
        </param-value>
    </context-param>

    <context-param>
        <param-name>contextInitializerClasses</param-name>
        <param-value>com.gumtree.user.spring.config.UserServiceContextInitializer</param-value>
    </context-param>

    <context-param>
        <param-name>spring.profiles.active</param-name>
        <param-value>prod</param-value>
    </context-param>

    <!-- 请求追踪过滤器 - 必须在其他过滤器之前 -->
    <filter>
        <display-name>RequestTraceFilter</display-name>
        <filter-name>RequestTraceFilter</filter-name>
        <filter-class>com.gumtree.user.trace.RequestTraceFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>RequestTraceFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <filter>
        <display-name>HystrixRequestContextServletFilter</display-name>
        <filter-name>HystrixRequestContextServletFilter</filter-name>
        <filter-class>com.gumtree.user.lib.hystrix.HystrixRequestContextFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>HystrixRequestContextServletFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <filter>
        <filter-name>encoding-filter</filter-name>
        <filter-class>
            org.springframework.web.filter.CharacterEncodingFilter
        </filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>

    <filter-mapping>
        <filter-name>encoding-filter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <servlet>
        <servlet-name>default</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextClass</param-name>
            <param-value>org.springframework.web.context.support.AnnotationConfigWebApplicationContext</param-value>
        </init-param>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>com.gumtree.user.spring.config.api.UserApiServletConfig</param-value>
        </init-param>
        <init-param>
            <param-name>spring.profiles.active</param-name>
            <param-value>prod</param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <servlet>
        <servlet-name>metrics</servlet-name>
        <servlet-class>io.prometheus.client.exporter.MetricsServlet</servlet-class>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>metrics</servlet-name>
        <url-pattern>/internal/metrics</url-pattern>
    </servlet-mapping>
    <context-param>
        <param-name>resteasy.providers</param-name>
        <param-value>com.gumtree.user.spring.config.jackson.JacksonObjectMapperProvider</param-value>
    </context-param>
    <listener>
        <listener-class>org.jboss.resteasy.plugins.server.servlet.ResteasyBootstrap</listener-class>
    </listener>
</web-app>

